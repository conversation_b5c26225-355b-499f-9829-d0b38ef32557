package com.whiskerguard.ai.service.agent.core.model;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 步骤定义类
 * Step Definition Class
 *
 * 定义工作流中单个步骤的配置信息
 * Define configuration information for a single step in the workflow
 *
 * <AUTHOR>
 * @since 1.0
 */
public class StepDefinition {

    /**
     * 步骤名称
     * Step name
     */
    private String name;

    /**
     * 步骤描述
     * Step description
     */
    private String description;

    /**
     * 步骤类型
     * Step type
     */
    private String type;

    /**
     * 执行顺序
     * Execution order
     */
    private Integer order;

    /**
     * 是否必需（影响错误处理策略）
     * Is required (affects error handling strategy)
     */
    private boolean required = false;

    /**
     * 是否可以并行执行
     * Can execute in parallel
     */
    private boolean parallel = false;

    /**
     * 步骤参数
     * Step parameters
     */
    private Map<String, Object> parameters = new HashMap<>();

    /**
     * 输入数据键列表（从上下文获取）
     * Input data keys (get from context)
     */
    private List<String> inputKeys;

    /**
     * 输出数据键（存储到上下文）
     * Output data key (store to context)
     */
    private String outputKey;

    /**
     * 依赖的其他步骤名称
     * Names of dependent steps
     */
    private List<String> dependsOn;

    /**
     * 超时时间（毫秒）
     * Timeout (milliseconds)
     */
    private Long timeoutMs;

    /**
     * 最大重试次数
     * Maximum retry count
     */
    private Integer maxRetries;

    // 构造函数 / Constructors

    public StepDefinition() {}

    public StepDefinition(String name, String type, Integer order) {
        this.name = name;
        this.type = type;
        this.order = order;
    }

    public StepDefinition(String name, String description, String type, Integer order) {
        this.name = name;
        this.description = description;
        this.type = type;
        this.order = order;
    }

    // 链式调用方法 / Fluent methods

    public StepDefinition withDescription(String description) {
        this.description = description;
        return this;
    }

    public StepDefinition withRequired(boolean required) {
        this.required = required;
        return this;
    }

    public StepDefinition withParallel(boolean parallel) {
        this.parallel = parallel;
        return this;
    }

    public StepDefinition withParameters(Map<String, Object> parameters) {
        this.parameters = parameters != null ? parameters : new HashMap<>();
        return this;
    }

    public StepDefinition withParameter(String key, Object value) {
        this.parameters.put(key, value);
        return this;
    }

    public StepDefinition withInputKeys(List<String> inputKeys) {
        this.inputKeys = inputKeys;
        return this;
    }

    public StepDefinition withOutputKey(String outputKey) {
        this.outputKey = outputKey;
        return this;
    }

    public StepDefinition withDependsOn(List<String> dependsOn) {
        this.dependsOn = dependsOn;
        return this;
    }

    public StepDefinition withTimeoutMs(Long timeoutMs) {
        this.timeoutMs = timeoutMs;
        return this;
    }

    public StepDefinition withMaxRetries(Integer maxRetries) {
        this.maxRetries = maxRetries;
        return this;
    }

    // Getter和Setter方法 / Getter and Setter methods

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Integer getOrder() {
        return order;
    }

    public void setOrder(Integer order) {
        this.order = order;
    }

    public boolean isRequired() {
        return required;
    }

    public void setRequired(boolean required) {
        this.required = required;
    }

    public boolean isParallel() {
        return parallel;
    }

    public void setParallel(boolean parallel) {
        this.parallel = parallel;
    }

    public Map<String, Object> getParameters() {
        return parameters;
    }

    public void setParameters(Map<String, Object> parameters) {
        this.parameters = parameters != null ? parameters : new HashMap<>();
    }

    public List<String> getInputKeys() {
        return inputKeys;
    }

    public void setInputKeys(List<String> inputKeys) {
        this.inputKeys = inputKeys;
    }

    public String getOutputKey() {
        return outputKey;
    }

    public void setOutputKey(String outputKey) {
        this.outputKey = outputKey;
    }

    public List<String> getDependsOn() {
        return dependsOn;
    }

    public void setDependsOn(List<String> dependsOn) {
        this.dependsOn = dependsOn;
    }

    public Long getTimeoutMs() {
        return timeoutMs;
    }

    public void setTimeoutMs(Long timeoutMs) {
        this.timeoutMs = timeoutMs;
    }

    public Integer getMaxRetries() {
        return maxRetries;
    }

    public void setMaxRetries(Integer maxRetries) {
        this.maxRetries = maxRetries;
    }

    @Override
    public String toString() {
        return (
            "StepDefinition{" +
            "name='" +
            name +
            '\'' +
            ", description='" +
            description +
            '\'' +
            ", type='" +
            type +
            '\'' +
            ", order=" +
            order +
            ", required=" +
            required +
            ", parallel=" +
            parallel +
            ", outputKey='" +
            outputKey +
            '\'' +
            '}'
        );
    }
}
