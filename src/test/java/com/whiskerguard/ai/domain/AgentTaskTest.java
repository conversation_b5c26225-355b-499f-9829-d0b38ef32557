package com.whiskerguard.ai.domain;

import static com.whiskerguard.ai.domain.AgentTaskTestSamples.*;
import static org.assertj.core.api.Assertions.assertThat;

import com.whiskerguard.ai.web.rest.TestUtil;
import org.junit.jupiter.api.Test;

class AgentTaskTest {

    @Test
    void equalsVerifier() throws Exception {
        TestUtil.equalsVerifier(AgentTask.class);
        AgentTask agentTask1 = getAgentTaskSample1();
        AgentTask agentTask2 = new AgentTask();
        assertThat(agentTask1).isNotEqualTo(agentTask2);

        agentTask2.setId(agentTask1.getId());
        assertThat(agentTask1).isEqualTo(agentTask2);

        agentTask2 = getAgentTaskSample2();
        assertThat(agentTask1).isNotEqualTo(agentTask2);
    }

    // 移除了 taskSteps 和 contexts 相关的测试方法
    // 因为对应的实体属性和方法已被删除以避免循环引用
    // Removed taskSteps and contexts related test methods
    // because corresponding entity properties and methods have been removed to avoid circular references
}
